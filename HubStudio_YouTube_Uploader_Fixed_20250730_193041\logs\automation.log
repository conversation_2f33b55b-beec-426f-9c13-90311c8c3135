2025-07-31 14:12:15 | INFO     | main:setup_logging:257 - 📝 日志系统初始化成功
2025-07-31 14:12:15 | INFO     | main:setup_logging:258 -    日志级别: INFO
2025-07-31 14:12:15 | INFO     | main:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\HubStudio_YouTube_Uploader_Fixed_20250730_193041\./logs/automation.log
2025-07-31 14:12:15 | INFO     | main:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-31 14:12:15 | INFO     | main:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-31 14:12:15 | INFO     | main:initialize:284 - 📋 初始化HubStudio管理器...
2025-07-31 14:12:15 | INFO     | main:initialize:297 - 🌐 启动HubStudio浏览器环境...
2025-07-31 14:12:16 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1281247135
2025-07-31 14:12:22 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 59301
2025-07-31 14:12:22 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 59301
2025-07-31 14:12:22 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 59301 可访问
2025-07-31 14:12:22 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-31 14:12:22 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-31 14:12:23 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-31 14:12:23 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-31 14:12:23 | INFO     | main:initialize:313 - 🧪 验证浏览器连接...
2025-07-31 14:12:23 | INFO     | main:initialize:316 - ✅ 浏览器连接验证成功
2025-07-31 14:12:23 | INFO     | main:initialize:325 - 📤 初始化统一视频上传器...
2025-07-31 14:12:23 | INFO     | main:initialize:328 - ✅ 视频上传器初始化成功
2025-07-31 14:12:23 | INFO     | main:initialize:334 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-31 14:12:23 | INFO     | main:initialize:335 - 📊 系统状态:
2025-07-31 14:12:23 | INFO     | main:initialize:336 -    - HubStudio管理器: ✅ 已连接
2025-07-31 14:12:23 | INFO     | main:initialize:337 -    - 浏览器环境: ✅ 已启动
2025-07-31 14:12:23 | INFO     | main:initialize:338 -    - 视频上传器: ✅ 已就绪
2025-07-31 14:12:23 | INFO     | main:upload_single_video:380 - 📁 视频文件: e913620b4484ff62e58a73612520e777.mp4 (27.2 MB)
2025-07-31 14:12:23 | INFO     | main:upload_single_video:390 - 🚀 开始上传视频: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:12:23 | INFO     | main:upload_single_video:391 - 📝 标题: 5
2025-07-31 14:12:23 | INFO     | main:upload_single_video:392 - 📄 描述: 通过HubStudio自动化脚本上传到YouTube的视频
2025-07-31 14:12:23 | INFO     | main:upload_single_video:393 - 🏷️ 标签: 自动化,HubStudio,YouTube,视频上传
2025-07-31 14:12:23 | INFO     | main:upload_single_video:394 - 👶 儿童内容: 否
2025-07-31 14:12:23 | INFO     | main:upload_single_video:397 - 🎬 开始执行上传流程...
2025-07-31 14:12:23 | INFO     | video_uploader_unified:upload_video:321 - 🎬 开始上传视频: e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:12:23 | INFO     | video_uploader_unified:validate_video_file:119 - 视频文件验证通过: e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:12:23 | INFO     | video_uploader_unified:upload_video:343 - 🌐 步骤1: 导航到YouTube Studio
2025-07-31 14:12:23 | INFO     | video_uploader_unified:_navigate_to_studio:402 - 导航到 YouTube Studio
2025-07-31 14:12:35 | INFO     | video_uploader_unified:_navigate_to_studio:432 - ✅ 成功导航到 YouTube Studio
2025-07-31 14:12:35 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\navigate_to_studio_1753942355.png
2025-07-31 14:12:35 | INFO     | video_uploader_unified:upload_video:349 - 🔘 步骤2: 点击上传按钮
2025-07-31 14:12:35 | INFO     | video_uploader_unified:_click_upload_button:449 - 查找并点击上传按钮
2025-07-31 14:12:38 | INFO     | video_uploader_unified:_click_upload_button:463 - 尝试直接导航到: https://studio.youtube.com/channel/upload
2025-07-31 14:12:44 | INFO     | video_uploader_unified:_click_upload_button:463 - 尝试直接导航到: https://www.youtube.com/upload
2025-07-31 14:12:50 | INFO     | video_uploader_unified:_click_upload_button:470 - ✅ 成功直接导航到上传页面
2025-07-31 14:12:50 | INFO     | video_uploader_unified:upload_video:355 - 📁 步骤3: 上传视频文件
2025-07-31 14:12:50 | INFO     | video_uploader_unified:_upload_file:643 - 🚀 开始上传文件: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:12:50 | INFO     | video_uploader_unified:_upload_file:651 - ✅ 文件路径: C:\Users\<USER>\Documents\WeChat Files\wxid_jutff6al2uat22\FileStorage\Video\2025-07\e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:12:53 | INFO     | video_uploader_unified:_upload_file:657 - 🔍 查找文件输入元素...
2025-07-31 14:12:53 | INFO     | video_uploader_unified:_upload_file:663 - 找到 1 个文件输入元素
2025-07-31 14:12:53 | INFO     | video_uploader_unified:_upload_file:668 - 尝试文件输入 1
2025-07-31 14:12:53 | INFO     | video_uploader_unified:_upload_file:672 - ✅ 文件已发送到输入 1
2025-07-31 14:12:55 | INFO     | video_uploader_unified:_upload_file:686 - 🎉 上传已开始！
2025-07-31 14:12:55 | INFO     | video_uploader_unified:upload_video:361 - 📝 步骤4: 填写视频详细信息
2025-07-31 14:12:55 | INFO     | video_uploader_unified:_fill_details:745 - 填写视频详细资讯
2025-07-31 14:12:56 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\details_page_start_1753942376.png
2025-07-31 14:12:56 | INFO     | video_uploader_unified:_fill_details:755 - 设置标题: 5
2025-07-31 14:12:56 | INFO     | video_uploader_unified:_set_input_field:1293 - 设置title: 5...
2025-07-31 14:13:01 | INFO     | video_uploader_unified:_set_input_field:1314 - ✅ title设置成功 (选择器 4)
2025-07-31 14:13:01 | INFO     | video_uploader_unified:_set_input_field:1293 - 设置描述: 通过HubStudio自动化脚本上传到YouTube的视频...
2025-07-31 14:13:02 | INFO     | video_uploader_unified:_set_input_field:1314 - ✅ 描述设置成功 (选择器 1)
2025-07-31 14:13:02 | INFO     | video_uploader_unified:_fill_details:793 - 设置儿童内容选项: 否
2025-07-31 14:13:05 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\before_kids_content_1753942385.png
2025-07-31 14:13:05 | INFO     | video_uploader_unified:_set_children_content_option:1388 - 设置儿童内容选项: 否
2025-07-31 14:13:06 | INFO     | video_uploader_unified:_set_children_content_option:1441 - ✅ 儿童内容选项设置成功 (选择器 1)
2025-07-31 14:13:06 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\after_kids_content_1753942386.png
2025-07-31 14:13:06 | INFO     | video_uploader_unified:_fill_details:817 - 设置标签: 自动化,HubStudio,YouTube,视频上传
2025-07-31 14:13:06 | INFO     | video_uploader_unified:_set_tags:889 - 设置标签: 自动化,HubStudio,YouTube,视频上传
2025-07-31 14:13:28 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 '自动化': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <div class="section-label-with-description style-scope ytcp-video-metadata-editor-basics">...</div>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8F4A]
	(No symbol) [0x00007FF63D2570A9]
	(No symbol) [0x00007FF63D254A72]
	(No symbol) [0x00007FF63D251B21]
	(No symbol) [0x00007FF63D250A31]
	(No symbol) [0x00007FF63D242224]
	(No symbol) [0x00007FF63D2771EA]
	(No symbol) [0x00007FF63D241AD6]
	(No symbol) [0x00007FF63D277400]
	(No symbol) [0x00007FF63D29F693]
	(No symbol) [0x00007FF63D276FC3]
	(No symbol) [0x00007FF63D23FEFE]
	(No symbol) [0x00007FF63D241183]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D36B214]
	(No symbol) [0x00007FF63D36B3B6]
	(No symbol) [0x00007FF63D35A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:13:29 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 'HubStudio': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <div class="section-label-with-description style-scope ytcp-video-metadata-editor-basics">...</div>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8F4A]
	(No symbol) [0x00007FF63D2570A9]
	(No symbol) [0x00007FF63D254A72]
	(No symbol) [0x00007FF63D251B21]
	(No symbol) [0x00007FF63D250A31]
	(No symbol) [0x00007FF63D242224]
	(No symbol) [0x00007FF63D2771EA]
	(No symbol) [0x00007FF63D241AD6]
	(No symbol) [0x00007FF63D277400]
	(No symbol) [0x00007FF63D29F693]
	(No symbol) [0x00007FF63D276FC3]
	(No symbol) [0x00007FF63D23FEFE]
	(No symbol) [0x00007FF63D241183]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D36B214]
	(No symbol) [0x00007FF63D36B3B6]
	(No symbol) [0x00007FF63D35A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:13:30 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 'YouTube': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <div class="section-label-with-description style-scope ytcp-video-metadata-editor-basics">...</div>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8F4A]
	(No symbol) [0x00007FF63D2570A9]
	(No symbol) [0x00007FF63D254A72]
	(No symbol) [0x00007FF63D251B21]
	(No symbol) [0x00007FF63D250A31]
	(No symbol) [0x00007FF63D242224]
	(No symbol) [0x00007FF63D2771EA]
	(No symbol) [0x00007FF63D241AD6]
	(No symbol) [0x00007FF63D277400]
	(No symbol) [0x00007FF63D29F693]
	(No symbol) [0x00007FF63D276FC3]
	(No symbol) [0x00007FF63D23FEFE]
	(No symbol) [0x00007FF63D241183]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D36B214]
	(No symbol) [0x00007FF63D36B3B6]
	(No symbol) [0x00007FF63D35A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:13:31 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 '视频上传': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <div class="section-label-with-description style-scope ytcp-video-metadata-editor-basics">...</div>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8F4A]
	(No symbol) [0x00007FF63D2570A9]
	(No symbol) [0x00007FF63D254A72]
	(No symbol) [0x00007FF63D251B21]
	(No symbol) [0x00007FF63D250A31]
	(No symbol) [0x00007FF63D242224]
	(No symbol) [0x00007FF63D2771EA]
	(No symbol) [0x00007FF63D241AD6]
	(No symbol) [0x00007FF63D277400]
	(No symbol) [0x00007FF63D29F693]
	(No symbol) [0x00007FF63D276FC3]
	(No symbol) [0x00007FF63D23FEFE]
	(No symbol) [0x00007FF63D241183]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D36B214]
	(No symbol) [0x00007FF63D36B3B6]
	(No symbol) [0x00007FF63D35A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:13:31 | INFO     | video_uploader_unified:_set_tags:947 - ✅ 标签设置完成
2025-07-31 14:13:31 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\details_filled_1753942411.png
2025-07-31 14:13:31 | INFO     | video_uploader_unified:_fill_details:824 - 查找下一步按钮...
2025-07-31 14:13:35 | INFO     | video_uploader_unified:_fill_details:866 - ✅ 成功点击下一步按钮
2025-07-31 14:13:38 | INFO     | video_uploader_unified:upload_video:367 - ⏭️ 步骤5: 跳过影片元素页面
2025-07-31 14:13:38 | INFO     | video_uploader_unified:_skip_video_elements:957 - 处理影片元素页面
2025-07-31 14:13:43 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\video_elements_page_1753942423.png
2025-07-31 14:13:46 | INFO     | video_uploader_unified:_skip_video_elements:1009 - ✅ 成功跳过影片元素页面
2025-07-31 14:13:49 | INFO     | video_uploader_unified:upload_video:373 - ✅ 步骤6: 跳过检查项目页面
2025-07-31 14:13:49 | INFO     | video_uploader_unified:_skip_checks:1033 - 处理检查项目页面
2025-07-31 14:13:54 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\checks_page_1753942434.png
2025-07-31 14:13:57 | INFO     | video_uploader_unified:_skip_checks:1085 - ✅ 成功跳过检查项目页面
2025-07-31 14:14:00 | INFO     | video_uploader_unified:upload_video:379 - 🚀 步骤7: 发布视频
2025-07-31 14:14:00 | INFO     | video_uploader_unified:_publish_video:1109 - 处理瀏覽權限和发布视频
2025-07-31 14:14:05 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\publish_page_1753942445.png
2025-07-31 14:14:07 | INFO     | video_uploader_unified:_publish_video:1123 - 设置瀏覽權限为公开
2025-07-31 14:14:08 | INFO     | video_uploader_unified:_publish_video:1156 - ✅ 设置瀏覽權限为公开
2025-07-31 14:14:09 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\visibility_set_public_1753942449.png
2025-07-31 14:14:17 | INFO     | video_uploader_unified:_publish_video:1222 - ✅ 成功点击发布按钮
2025-07-31 14:14:17 | INFO     | video_uploader_unified:_publish_video:1236 - 等待发布完成...
2025-07-31 14:14:27 | ERROR    | video_uploader_unified:_take_screenshot:1289 - 保存截图失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8F4A]
	(No symbol) [0x00007FF63D1E4D45]
	(No symbol) [0x00007FF63D2096DA]
	(No symbol) [0x00007FF63D27EF7F]
	(No symbol) [0x00007FF63D29EF82]
	(No symbol) [0x00007FF63D276FC3]
	(No symbol) [0x00007FF63D23FEFE]
	(No symbol) [0x00007FF63D241183]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D36B214]
	(No symbol) [0x00007FF63D36B3B6]
	(No symbol) [0x00007FF63D35A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:14:27 | ERROR    | video_uploader_unified:_publish_video:1272 - 发布视频失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8D7C]
	(No symbol) [0x00007FF63D23F11F]
	(No symbol) [0x00007FF63D2770B2]
	(No symbol) [0x00007FF63D271A49]
	(No symbol) [0x00007FF63D270AF9]
	(No symbol) [0x00007FF63D1C5595]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D1C41AE]
	GetMachineCode [0x00007FF63D7F60D8+3271176]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:14:27 | ERROR    | video_uploader_unified:_take_screenshot:1289 - 保存截图失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8D7C]
	(No symbol) [0x00007FF63D23F11F]
	(No symbol) [0x00007FF63D2770B2]
	(No symbol) [0x00007FF63D271A49]
	(No symbol) [0x00007FF63D270AF9]
	(No symbol) [0x00007FF63D1C5595]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D1C41AE]
	GetMachineCode [0x00007FF63D7F60D8+3271176]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:14:27 | ERROR    | video_uploader_unified:upload_video:381 - ❌ 发布视频失败
2025-07-31 14:14:27 | ERROR    | main:upload_single_video:404 - ❌ YouTube视频上传失败: e913620b4484ff62e58a73612520e777.mp4
2025-07-31 14:14:27 | ERROR    | video_uploader_unified:_take_screenshot:1289 - 保存截图失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF63D407EB5+84709]
	(No symbol) [0x00007FF63D364200]
	(No symbol) [0x00007FF63D1F8D7C]
	(No symbol) [0x00007FF63D23F11F]
	(No symbol) [0x00007FF63D2770B2]
	(No symbol) [0x00007FF63D271A49]
	(No symbol) [0x00007FF63D270AF9]
	(No symbol) [0x00007FF63D1C5595]
	GetMachineCode [0x00007FF63D73048D+2461117]
	GetMachineCode [0x00007FF63D783B03+2802739]
	GetMachineCode [0x00007FF63D77953D+2760301]
	GetMachineCode [0x00007FF63D4DED0A+30266]
	(No symbol) [0x00007FF63D36EC4F]
	(No symbol) [0x00007FF63D1C41AE]
	GetMachineCode [0x00007FF63D7F60D8+3271176]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]

2025-07-31 14:14:27 | INFO     | main:upload_single_video:411 - 📸 错误截图已保存: error_1753942467.png
2025-07-31 14:14:29 | INFO     | main:cleanup:499 - 清理资源...
2025-07-31 14:14:31 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-31 14:14:31 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-31 14:14:48 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1281247135
2025-07-31 14:14:48 | WARNING  | browser_manager:stop_browser_profile:395 - 停止浏览器失败: 未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)
2025-07-31 14:14:48 | INFO     | main:cleanup:507 - 资源清理完成
2025-07-31 16:42:31 | INFO     | __main__:setup_logging:257 - 📝 日志系统初始化成功
2025-07-31 16:42:31 | INFO     | __main__:setup_logging:258 -    日志级别: INFO
2025-07-31 16:42:31 | INFO     | __main__:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\HubStudio_YouTube_Uploader_Fixed_20250730_193041\./logs/automation.log
2025-07-31 16:42:31 | INFO     | __main__:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-31 16:42:31 | INFO     | __main__:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-31 16:42:31 | INFO     | __main__:initialize:284 - 📋 初始化HubStudio管理器...
