#!/usr/bin/env python3
"""
模拟测试：验证分页逻辑能够处理超过200个环境的情况
"""

import json
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def create_mock_response(page, page_size, total_environments):
    """创建模拟的API响应"""
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_environments)
    
    # 生成当前页的环境数据
    environments = []
    for i in range(start_idx, end_idx):
        env = {
            'containerCode': 1000000 + i,
            'containerName': f'测试环境{i+1}',
            'tagName': None,
            'tagCode': None,
            'remark': None,
            'proxyTypeName': '不使用代理',
            'proxyHost': None,
            'proxyPort': None,
            'proxyAccount': None,
            'proxyPassword': '',
            'asDynamicType': 0,
            'lastUsedIp': f'192.168.1.{(i % 254) + 1}',
            'lastCountry': 'China',
            'lastRegion': 'Beijing',
            'lastCity': 'Beijing',
            'referenceCountryName': None,
            'referenceCountryCode': None,
            'referenceRegionCode': None,
            'referenceCity': None,
            'referenceIp': '',
            'openTime': '2025-07-31 15:24:50',
            'allOpenTime': '2025-07-31 15:24:50',
            'createTime': '2025-07-27 00:37:17',
            'coreVersion': 133,
            'accounts': None,
            'ipDatabaseChannel': 1,
            'ipProtocolType': 1,
            'refreshUrl': None,
            'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'
        }
        environments.append(env)
    
    # 创建响应数据
    response_data = {
        'code': 0,
        'msg': 'Success',
        'requestId': f'mock_request_{page}',
        'timestamp': *************,
        'data': {
            'list': environments,
            'total': total_environments
        }
    }
    
    return response_data


def test_pagination_logic(total_environments):
    """测试分页逻辑"""
    print(f"\n测试场景：{total_environments} 个环境")
    print("-" * 50)
    
    # 模拟分页参数
    page_size = 200
    current_page = 1
    total_pages = (total_environments + page_size - 1) // page_size
    all_environments = []
    
    print(f"预期总页数: {total_pages}")
    
    # 模拟分页请求
    for page in range(1, total_pages + 1):
        response_data = create_mock_response(page, page_size, total_environments)
        environments = response_data['data']['list']
        total_count = response_data['data']['total']
        
        print(f"第 {page} 页: 获取到 {len(environments)} 个环境")
        all_environments.extend(environments)
        
        # 验证分页计算
        calculated_total_pages = (total_count + page_size - 1) // page_size
        if calculated_total_pages != total_pages:
            print(f"⚠️  分页计算不一致: 预期 {total_pages}, 计算得到 {calculated_total_pages}")
    
    print(f"✅ 总共获取到 {len(all_environments)} 个环境")
    
    # 验证结果
    if len(all_environments) == total_environments:
        print("✅ 分页逻辑正确，成功获取所有环境")
        return True
    else:
        print(f"❌ 分页逻辑错误，预期 {total_environments} 个，实际获取 {len(all_environments)} 个")
        return False


def test_browser_manager_pagination():
    """测试BrowserManager的分页逻辑"""
    print("\n" + "="*60)
    print("测试 BrowserManager 分页逻辑")
    print("="*60)
    
    from browser_manager import HubStudioManager
    import configparser
    
    # 创建模拟配置
    config = configparser.ConfigParser()
    config.add_section('HUBSTUDIO')
    config.set('HUBSTUDIO', 'api_id', 'mock_api_id')
    config.set('HUBSTUDIO', 'api_secret', 'mock_api_secret')
    config.add_section('BROWSER')
    config.set('BROWSER', 'api_url', 'http://localhost:6873')
    
    manager = HubStudioManager(config)
    
    # 测试不同数量的环境
    test_cases = [150, 200, 250, 500, 1000]
    
    for total_envs in test_cases:
        print(f"\n模拟测试：{total_envs} 个环境")
        
        def mock_post(url, json=None, headers=None, timeout=None):
            """模拟requests.post"""
            page = json.get('current', 1)
            page_size = json.get('size', 200)
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = create_mock_response(page, page_size, total_envs)
            return mock_response
        
        # 使用mock替换requests.post
        with patch('browser_manager.requests.post', side_effect=mock_post):
            environments = manager.get_environment_list()
            
            if len(environments) == total_envs:
                print(f"✅ 成功处理 {total_envs} 个环境")
            else:
                print(f"❌ 处理失败，预期 {total_envs} 个，实际 {len(environments)} 个")


def test_gui_controller_pagination():
    """测试GUI Controller的分页逻辑"""
    print("\n" + "="*60)
    print("测试 GUI Controller 分页逻辑")
    print("="*60)
    
    from gui.gui_controller import GUIController
    
    controller = GUIController()
    
    # 测试不同数量的环境
    test_cases = [150, 200, 250, 500, 1000]
    
    for total_envs in test_cases:
        print(f"\n模拟测试：{total_envs} 个环境")
        
        def mock_post(url, json=None, headers=None, timeout=None):
            """模拟requests.post"""
            page = json.get('current', 1)
            page_size = json.get('size', 200)
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = create_mock_response(page, page_size, total_envs)
            return mock_response
        
        # 使用mock替换requests.post
        with patch('gui.gui_controller.requests.post', side_effect=mock_post):
            env_list = controller._fetch_environment_list(
                'http://localhost:6873', 
                'mock_api_id', 
                'mock_api_secret'
            )
            
            if len(env_list) == total_envs:
                print(f"✅ 成功处理 {total_envs} 个环境")
            else:
                print(f"❌ 处理失败，预期 {total_envs} 个，实际 {len(env_list)} 个")


def main():
    """主测试函数"""
    print("Hub Studio 分页逻辑模拟测试")
    print("="*60)
    
    # 测试基础分页逻辑
    print("\n1. 测试基础分页逻辑")
    test_cases = [50, 150, 200, 250, 500, 1000, 2500]
    
    all_passed = True
    for total_envs in test_cases:
        if not test_pagination_logic(total_envs):
            all_passed = False
    
    if all_passed:
        print("\n✅ 所有基础分页逻辑测试通过")
    else:
        print("\n❌ 部分基础分页逻辑测试失败")
    
    # 测试BrowserManager
    test_browser_manager_pagination()
    
    # 测试GUI Controller
    test_gui_controller_pagination()
    
    print("\n" + "="*60)
    print("模拟测试总结")
    print("="*60)
    print("✅ 分页逻辑已实现，能够处理超过200个环境的情况")
    print("✅ 系统现在可以检测和处理无限数量的Hub Studio环境")
    print("✅ 移除了原有的200个环境限制")
    print("\n模拟测试完成！")


if __name__ == "__main__":
    main()
