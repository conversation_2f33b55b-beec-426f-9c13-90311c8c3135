#!/usr/bin/env python3
"""
测试脚本：验证Hub Studio浏览器环境无限制检测功能
"""

import configparser
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from browser_manager import HubStudioManager
from gui.gui_controller import GUIController


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = project_root / 'config.ini'
    
    if config_path.exists():
        config.read(config_path, encoding='utf-8')
        print(f"✅ 配置文件加载成功: {config_path}")
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    return config


def test_browser_manager_unlimited_detection(config):
    """测试browser_manager的无限制环境检测"""
    print("\n" + "="*60)
    print("测试 BrowserManager 无限制环境检测")
    print("="*60)
    
    try:
        # 创建HubStudioManager实例
        manager = HubStudioManager(config)
        
        # 获取环境列表
        print("🔍 开始获取环境列表...")
        environments = manager.get_environment_list()
        
        if environments:
            print(f"✅ 成功检测到 {len(environments)} 个Hub Studio环境")
            print("\n环境详情:")
            for i, env in enumerate(environments[:10], 1):  # 只显示前10个
                print(f"  {i}. ID: {env['id']}, 名称: {env['name']}, 状态: {env['status']}")
            
            if len(environments) > 10:
                print(f"  ... 还有 {len(environments) - 10} 个环境")
                
            # 验证是否突破了原来的200个限制
            if len(environments) > 200:
                print(f"🎉 成功突破原有200个环境的限制！检测到 {len(environments)} 个环境")
            elif len(environments) == 200:
                print("⚠️  检测到正好200个环境，可能仍有限制或实际就是200个")
            else:
                print(f"ℹ️  检测到 {len(environments)} 个环境（少于200个）")
                
        else:
            print("❌ 未检测到任何环境")
            print("可能的原因：")
            print("  1. API凭证配置错误")
            print("  2. HubStudio客户端未运行")
            print("  3. 网络连接问题")
            
        return len(environments) if environments else 0
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 0


def test_gui_controller_unlimited_detection(config):
    """测试GUI控制器的无限制环境检测"""
    print("\n" + "="*60)
    print("测试 GUI Controller 无限制环境检测")
    print("="*60)
    
    try:
        # 创建GUI控制器实例
        controller = GUIController()
        
        # 获取API配置
        api_url = config.get('BROWSER', 'api_url', fallback='http://localhost:6873')
        api_id = config.get('HUBSTUDIO', 'api_id', fallback='')
        api_secret = config.get('HUBSTUDIO', 'api_secret', fallback='')
        
        if not api_id or not api_secret:
            print("❌ API凭证未配置，跳过GUI控制器测试")
            return 0
        
        # 获取环境列表
        print("🔍 开始获取环境列表...")
        env_list = controller._fetch_environment_list(api_url, api_id, api_secret)
        
        if env_list:
            print(f"✅ 成功检测到 {len(env_list)} 个Hub Studio环境")
            print("\n环境详情:")
            for i, env in enumerate(env_list[:10], 1):  # 只显示前10个
                print(f"  {i}. {env}")
            
            if len(env_list) > 10:
                print(f"  ... 还有 {len(env_list) - 10} 个环境")
                
            # 验证是否突破了原来的200个限制
            if len(env_list) > 200:
                print(f"🎉 成功突破原有200个环境的限制！检测到 {len(env_list)} 个环境")
            elif len(env_list) == 200:
                print("⚠️  检测到正好200个环境，可能仍有限制或实际就是200个")
            else:
                print(f"ℹ️  检测到 {len(env_list)} 个环境（少于200个）")
                
        else:
            print("❌ 未检测到任何环境")
            
        return len(env_list) if env_list else 0
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 0


def main():
    """主测试函数"""
    print("Hub Studio 无限制浏览器环境检测测试")
    print("="*60)
    
    # 加载配置
    config = load_config()
    if not config:
        print("❌ 无法加载配置文件，测试终止")
        return
    
    # 检查API配置
    api_id = config.get('HUBSTUDIO', 'api_id', fallback='')
    api_secret = config.get('HUBSTUDIO', 'api_secret', fallback='')
    
    if not api_id or not api_secret:
        print("❌ HubStudio API凭证未配置")
        print("请在config.ini文件中配置以下参数：")
        print("  [HUBSTUDIO]")
        print("  api_id = 你的API ID")
        print("  api_secret = 你的API Secret")
        return
    
    print(f"✅ API配置检查通过")
    print(f"   API ID: {api_id[:10]}...")
    
    # 执行测试
    browser_manager_count = test_browser_manager_unlimited_detection(config)
    gui_controller_count = test_gui_controller_unlimited_detection(config)
    
    # 总结测试结果
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    print(f"BrowserManager 检测到环境数量: {browser_manager_count}")
    print(f"GUI Controller 检测到环境数量: {gui_controller_count}")
    
    if browser_manager_count > 0 and gui_controller_count > 0:
        if browser_manager_count == gui_controller_count:
            print("✅ 两个组件检测结果一致")
        else:
            print("⚠️  两个组件检测结果不一致，可能存在问题")
            
        if max(browser_manager_count, gui_controller_count) > 200:
            print("🎉 成功实现无限制环境检测！")
        else:
            print("ℹ️  当前环境数量未超过200个，无法验证是否突破限制")
    else:
        print("❌ 测试失败，请检查配置和网络连接")
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
